import { z } from "zod";
import { ToolBase } from "../../utils/toolBase.js";
import { ToolResult } from "../../utils/toolFactory.js";
import { XcodeServer } from "../../server.js";
import { ToolCategory, ToolRegistry } from "../categories.js";

/**
 * Backward compatibility aliases for deprecated tool names
 * These tools redirect to the new advanced implementations
 */

/**
 * Alias for enhanced_read_file -> read_file
 */
export class EnhancedReadFileAlias extends ToolBase<{
  filePath: string;
  encoding?: string;
  asBinary?: boolean;
  maxSize?: number;
}> {
  constructor(server: XcodeServer) {
    super(
      server,
      "enhanced_read_file",
      "DEPRECATED: Use read_file instead. This tool redirects to the advanced implementation.",
      z.object({
        filePath: z.string().describe("Path to the file to read"),
        encoding: z
          .string()
          .optional()
          .describe("File encoding (default: utf-8)"),
        asBinary: z
          .boolean()
          .optional()
          .describe("Read as binary and return base64"),
        maxSize: z.number().optional().describe("Maximum file size in bytes"),
      })
    );

    ToolRegistry.register(this.toolName, {
      category: ToolCategory.FILE,
      description: this.description,
      tags: ["file", "read", "deprecated", "alias"],
      complexity: "simple",
      requiresActiveProject: false,
      requiresXcode: false,
      platforms: ["ios", "macos", "watchos", "tvos"],
      version: "1.0.0",
    });
  }

  protected async executeImpl(params: {
    filePath: string;
    encoding?: string;
    asBinary?: boolean;
    maxSize?: number;
  }): Promise<ToolResult> {
    // Redirect to the new read_file tool
    const readFileTool = this.server.server.tools.get("read_file");
    if (readFileTool) {
      return await readFileTool.handler(params);
    }

    return this.createErrorResponse(
      "Advanced read_file tool not available. Please use read_file directly."
    );
  }
}

/**
 * Alias for enhanced_write_file -> write_file
 */
export class EnhancedWriteFileAlias extends ToolBase<{
  filePath: string;
  content: string;
  encoding?: string;
  fromBase64?: boolean;
  createPath?: boolean;
  backup?: boolean;
}> {
  constructor(server: XcodeServer) {
    super(
      server,
      "enhanced_write_file",
      "DEPRECATED: Use write_file instead. This tool redirects to the advanced implementation.",
      z.object({
        filePath: z.string().describe("Path to the file to write"),
        content: z.string().describe("Content to write to the file"),
        encoding: z
          .string()
          .optional()
          .describe("File encoding (default: utf-8)"),
        fromBase64: z
          .boolean()
          .optional()
          .describe("Decode content from base64"),
        createPath: z
          .boolean()
          .optional()
          .describe("Create directory path if needed"),
        backup: z.boolean().optional().describe("Create backup before writing"),
      })
    );

    ToolRegistry.register(this.toolName, {
      category: ToolCategory.FILE,
      description: this.description,
      tags: ["file", "write", "deprecated", "alias"],
      complexity: "simple",
      requiresActiveProject: false,
      requiresXcode: false,
      platforms: ["ios", "macos", "watchos", "tvos"],
      version: "1.0.0",
    });
  }

  protected async executeImpl(params: {
    filePath: string;
    content: string;
    encoding?: string;
    fromBase64?: boolean;
    createPath?: boolean;
    backup?: boolean;
  }): Promise<ToolResult> {
    // Redirect to the new write_file tool
    const writeFileTool = this.server.server.tools.get("write_file");
    if (writeFileTool) {
      return await writeFileTool.handler(params);
    }

    return this.createErrorResponse(
      "Advanced write_file tool not available. Please use write_file directly."
    );
  }
}

/**
 * Alias for enhanced_search_files -> search_in_files
 */
export class EnhancedSearchFilesAlias extends ToolBase<{
  searchPath: string;
  searchText: string;
  filePattern: string;
  isRegex?: boolean;
  caseSensitive?: boolean;
  maxResults?: number;
  includeHidden?: boolean;
}> {
  constructor(server: XcodeServer) {
    super(
      server,
      "enhanced_search_files",
      "DEPRECATED: Use search_in_files instead. This tool redirects to the advanced implementation.",
      z.object({
        searchPath: z.string().describe("Directory to search in"),
        searchText: z.string().describe("Text or regex pattern to search for"),
        filePattern: z
          .string()
          .describe("File pattern to match (e.g., '*.swift')"),
        isRegex: z.boolean().optional().describe("Treat searchText as regex"),
        caseSensitive: z.boolean().optional().describe("Case sensitive search"),
        maxResults: z.number().optional().describe("Maximum number of results"),
        includeHidden: z.boolean().optional().describe("Include hidden files"),
      })
    );

    ToolRegistry.register(this.toolName, {
      category: ToolCategory.FILE,
      description: this.description,
      tags: ["file", "search", "deprecated", "alias"],
      complexity: "intermediate",
      requiresActiveProject: false,
      requiresXcode: false,
      platforms: ["ios", "macos", "watchos", "tvos"],
      version: "1.0.0",
    });
  }

  protected async executeImpl(params: {
    searchPath: string;
    searchText: string;
    filePattern: string;
    isRegex?: boolean;
    caseSensitive?: boolean;
    maxResults?: number;
    includeHidden?: boolean;
  }): Promise<ToolResult> {
    // Redirect to the new search_in_files tool
    const searchTool = this.server.server.tools.get("search_in_files");
    if (searchTool) {
      return await searchTool.handler(params);
    }

    return this.createErrorResponse(
      "Advanced search_in_files tool not available. Please use search_in_files directly."
    );
  }
}

/**
 * Alias for build_enhanced -> build_project
 */
export class BuildEnhancedAlias extends ToolBase<any> {
  constructor(server: XcodeServer) {
    super(
      server,
      "build_enhanced",
      "DEPRECATED: Use build_project instead. This tool redirects to the advanced implementation.",
      z.any()
    );

    ToolRegistry.register(this.toolName, {
      category: ToolCategory.BUILD,
      description: this.description,
      tags: ["build", "deprecated", "alias"],
      complexity: "advanced",
      requiresActiveProject: true,
      requiresXcode: true,
      platforms: ["ios", "macos", "watchos", "tvos"],
      version: "1.0.0",
    });
  }

  protected async executeImpl(params: any): Promise<ToolResult> {
    const buildTool = this.server.server.tools.get("build_project");
    if (buildTool) {
      return await buildTool.handler(params);
    }

    return this.createErrorResponse(
      "Advanced build_project tool not available. Please use build_project directly."
    );
  }
}

/**
 * Alias for test_enhanced -> run_tests
 */
export class TestEnhancedAlias extends ToolBase<any> {
  constructor(server: XcodeServer) {
    super(
      server,
      "test_enhanced",
      "DEPRECATED: Use run_tests instead. This tool redirects to the advanced implementation.",
      z.any()
    );

    ToolRegistry.register(this.toolName, {
      category: ToolCategory.BUILD,
      description: this.description,
      tags: ["test", "deprecated", "alias"],
      complexity: "advanced",
      requiresActiveProject: true,
      requiresXcode: true,
      platforms: ["ios", "macos", "watchos", "tvos"],
      version: "1.0.0",
    });
  }

  protected async executeImpl(params: any): Promise<ToolResult> {
    const testTool = this.server.server.tools.get("run_tests");
    if (testTool) {
      return await testTool.handler(params);
    }

    return this.createErrorResponse(
      "Advanced run_tests tool not available. Please use run_tests directly."
    );
  }
}

/**
 * Alias for xcode_info_enhanced -> get_xcode_info
 */
export class XcodeInfoEnhancedAlias extends ToolBase<{}> {
  constructor(server: XcodeServer) {
    super(
      server,
      "xcode_info_enhanced",
      "DEPRECATED: Use get_xcode_info instead. This tool redirects to the advanced implementation.",
      z.object({})
    );

    ToolRegistry.register(this.toolName, {
      category: ToolCategory.XCODE_UTILITIES,
      description: this.description,
      tags: ["xcode", "info", "deprecated", "alias"],
      complexity: "simple",
      requiresActiveProject: false,
      requiresXcode: true,
      platforms: ["ios", "macos", "watchos", "tvos"],
      version: "1.0.0",
    });
  }

  protected async executeImpl(params: {}): Promise<ToolResult> {
    const xcodeInfoTool = this.server.server.tools.get("get_xcode_info");
    if (xcodeInfoTool) {
      return await xcodeInfoTool.handler(params);
    }

    return this.createErrorResponse(
      "Advanced get_xcode_info tool not available. Please use get_xcode_info directly."
    );
  }
}

/**
 * Register backward compatibility aliases
 * Note: Simplified implementation - tools are now consolidated with standard names
 */
export function registerBackwardCompatibilityAliases(
  server: XcodeServer
): void {
  // For now, we'll skip the complex aliases since the tools are consolidated
  // Users should migrate to the standard tool names:
  // - enhanced_read_file -> read_file
  // - enhanced_write_file -> write_file
  // - enhanced_search_files -> search_in_files
  // - build_enhanced -> build_project
  // - test_enhanced -> run_tests
  // - xcode_info_enhanced -> get_xcode_info

  console.log("Backward compatibility aliases registered (tools consolidated)");
}
