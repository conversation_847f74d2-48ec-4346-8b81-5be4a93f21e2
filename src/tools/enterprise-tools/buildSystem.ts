import { z } from "zod";
import { ProjectToolBase } from "../../utils/toolBase.js";
import { ToolResult } from "../../utils/toolFactory.js";
import { XcodeServer } from "../../server.js";
import { ToolCategory, ToolRegistry } from "../categories.js";
import { AdvancedCacheManager } from "../../utils/advancedCache.js";

/**
 * Advanced build tool with intelligent caching and optimization
 */
export class AdvancedBuildTool extends ProjectToolBase<{
  scheme: string;
  configuration: string;
  destination?: string;
  sdk?: string;
  derivedDataPath?: string;
  jobs?: number;
  enableCaching?: boolean;
  cleanBuild?: boolean;
  analyzeCode?: boolean;
  generateCoverage?: boolean;
}> {
  private buildCache = AdvancedCacheManager.getCache("build-results", {
    maxSize: 100,
    defaultTtl: 3600000, // 1 hour
  });

  constructor(server: XcodeServer) {
    super(
      server,
      "build_project",
      "Build project with intelligent caching, optimization, and comprehensive reporting",
      z.object({
        scheme: z.string().describe("Build scheme name"),
        configuration: z
          .string()
          .describe("Build configuration (Debug/Release)"),
        destination: z.string().optional().describe("Build destination"),
        sdk: z.string().optional().describe("SDK to use"),
        derivedDataPath: z.string().optional().describe("Derived data path"),
        jobs: z.number().optional().describe("Number of concurrent build jobs"),
        enableCaching: z
          .boolean()
          .optional()
          .describe("Enable intelligent build caching"),
        cleanBuild: z.boolean().optional().describe("Perform clean build"),
        analyzeCode: z.boolean().optional().describe("Run static analysis"),
        generateCoverage: z
          .boolean()
          .optional()
          .describe("Generate code coverage"),
      })
    );

    ToolRegistry.register(this.toolName, {
      category: ToolCategory.BUILD,
      description: this.description,
      tags: ["build", "advanced", "caching", "optimization", "intelligent"],
      complexity: "advanced",
      requiresActiveProject: true,
      requiresXcode: true,
      platforms: ["ios", "macos", "watchos", "tvos"],
      version: "2.0.0",
    });
  }

  protected async executeImpl(params: {
    scheme: string;
    configuration: string;
    destination?: string;
    sdk?: string;
    derivedDataPath?: string;
    jobs?: number;
    enableCaching?: boolean;
    cleanBuild?: boolean;
    analyzeCode?: boolean;
    generateCoverage?: boolean;
  }): Promise<ToolResult> {
    try {
      this.ensureActiveProject();
      const projectDir = this.getActiveProjectDirectory();

      // Generate cache key for build
      const cacheKey = this.generateBuildCacheKey(params);

      // Check cache if enabled
      if (params.enableCaching && !params.cleanBuild) {
        const cachedResult = await this.buildCache.get(cacheKey);
        if (cachedResult) {
          return this.createSuccessResponse(
            "Build completed using cached results",
            { ...cachedResult, fromCache: true }
          );
        }
      }

      const startTime = Date.now();

      // Build command arguments
      const args = ["xcodebuild"];

      // Add scheme and configuration
      args.push("-scheme", params.scheme);
      args.push("-configuration", params.configuration);

      // Add optional parameters
      if (params.destination) {
        args.push("-destination", params.destination);
      }

      if (params.sdk) {
        args.push("-sdk", params.sdk);
      }

      if (params.derivedDataPath) {
        args.push("-derivedDataPath", params.derivedDataPath);
      }

      if (params.jobs) {
        args.push("-jobs", params.jobs.toString());
      }

      // Add analysis if requested
      if (params.analyzeCode) {
        args.push("analyze");
      }

      // Add coverage if requested
      if (params.generateCoverage) {
        args.push("-enableCodeCoverage", "YES");
      }

      // Clean build if requested
      if (params.cleanBuild) {
        const cleanArgs = [...args];
        cleanArgs.push("clean");
        await this.executeCommand("xcrun", cleanArgs, { cwd: projectDir });
      }

      // Build
      args.push("build");

      const result = await this.executeCommand("xcrun", args, {
        cwd: projectDir,
        timeout: 600000, // 10 minutes
      });

      const buildTime = Date.now() - startTime;

      // Parse build results
      const buildInfo = this.parseBuildOutput(result.stdout, result.stderr);

      const buildResult = {
        scheme: params.scheme,
        configuration: params.configuration,
        buildTime,
        success: true,
        ...buildInfo,
        timestamp: new Date().toISOString(),
      };

      // Cache successful build if enabled
      if (params.enableCaching && buildInfo.warnings.length === 0) {
        await this.buildCache.set(cacheKey, buildResult, {
          dependencies: [
            { type: "directory", path: projectDir },
            { type: "file", path: `${projectDir}/project.pbxproj` },
          ],
          tags: ["build", params.scheme, params.configuration],
        });
      }

      return this.createSuccessResponse(
        `Build completed successfully in ${buildTime}ms`,
        buildResult
      );
    } catch (error) {
      const buildError = this.parseBuildError(error);
      return this.createErrorResponse(
        `Build failed: ${params.scheme}`,
        buildError
      );
    }
  }

  private generateBuildCacheKey(params: any): string {
    const keyData = {
      scheme: params.scheme,
      configuration: params.configuration,
      destination: params.destination,
      sdk: params.sdk,
      projectPath: this.server.activeProject?.path,
    };

    return `build_${Buffer.from(JSON.stringify(keyData)).toString("base64")}`;
  }

  private parseBuildOutput(
    stdout: string,
    stderr: string
  ): {
    warnings: string[];
    errors: string[];
    buildProducts: string[];
    compiledFiles: number;
    linkedLibraries: string[];
  } {
    const warnings: string[] = [];
    const errors: string[] = [];
    const buildProducts: string[] = [];
    const linkedLibraries: string[] = [];
    let compiledFiles = 0;

    const allOutput = stdout + "\n" + stderr;
    const lines = allOutput.split("\n");

    for (const line of lines) {
      if (line.includes("warning:")) {
        warnings.push(line.trim());
      } else if (line.includes("error:")) {
        errors.push(line.trim());
      } else if (line.includes("CompileC")) {
        compiledFiles++;
      } else if (line.includes("Ld ") && line.includes(".app/")) {
        const match = line.match(/Ld\s+(.+\.app)/);
        if (match) {
          buildProducts.push(match[1]);
        }
      } else if (line.includes("Linking") && line.includes(".framework")) {
        const match = line.match(/(\w+\.framework)/);
        if (match) {
          linkedLibraries.push(match[1]);
        }
      }
    }

    return {
      warnings,
      errors,
      buildProducts,
      compiledFiles,
      linkedLibraries,
    };
  }

  private parseBuildError(error: any): Error {
    if (error instanceof Error) {
      // Try to extract meaningful error from xcodebuild output
      const output = error.message;

      // Look for specific error patterns
      const patterns = [
        /error:\s*(.+)/i,
        /fatal error:\s*(.+)/i,
        /linker command failed with exit code \d+ \(use -v to see invocation\)/i,
        /Command failed with exit code \d+/i,
      ];

      for (const pattern of patterns) {
        const match = output.match(pattern);
        if (match) {
          return new Error(match[1] || match[0]);
        }
      }
    }

    return error instanceof Error ? error : new Error(String(error));
  }
}

/**
 * Advanced test runner with parallel execution and detailed reporting
 */
export class AdvancedTestTool extends ProjectToolBase<{
  scheme?: string;
  destination?: string;
  testPlan?: string;
  onlyTesting?: string[];
  skipTesting?: string[];
  enableCodeCoverage?: boolean;
  parallel?: boolean;
  maxConcurrency?: number;
  resultBundlePath?: string;
  generateReport?: boolean;
}> {
  constructor(server: XcodeServer) {
    super(
      server,
      "run_tests",
      "Run tests with parallel execution, detailed reporting, and intelligent test selection",
      z.object({
        scheme: z.string().optional().describe("Test scheme"),
        destination: z.string().optional().describe("Test destination"),
        testPlan: z.string().optional().describe("Test plan name"),
        onlyTesting: z
          .array(z.string())
          .optional()
          .describe("Specific tests to run"),
        skipTesting: z.array(z.string()).optional().describe("Tests to skip"),
        enableCodeCoverage: z
          .boolean()
          .optional()
          .describe("Enable code coverage"),
        parallel: z.boolean().optional().describe("Run tests in parallel"),
        maxConcurrency: z
          .number()
          .optional()
          .describe("Maximum parallel test processes"),
        resultBundlePath: z
          .string()
          .optional()
          .describe("Path for test results bundle"),
        generateReport: z
          .boolean()
          .optional()
          .describe("Generate detailed test report"),
      })
    );

    ToolRegistry.register(this.toolName, {
      category: ToolCategory.BUILD,
      description: this.description,
      tags: [
        "test",
        "advanced",
        "parallel",
        "coverage",
        "reporting",
        "intelligent",
      ],
      complexity: "advanced",
      requiresActiveProject: true,
      requiresXcode: true,
      platforms: ["ios", "macos", "watchos", "tvos"],
      version: "2.0.0",
    });
  }

  protected async executeImpl(params: {
    scheme?: string;
    destination?: string;
    testPlan?: string;
    onlyTesting?: string[];
    skipTesting?: string[];
    enableCodeCoverage?: boolean;
    parallel?: boolean;
    maxConcurrency?: number;
    resultBundlePath?: string;
    generateReport?: boolean;
  }): Promise<ToolResult> {
    try {
      this.ensureActiveProject();
      const projectDir = this.getActiveProjectDirectory();

      const startTime = Date.now();

      // Build test command
      const args = ["xcodebuild", "test"];

      // Add scheme
      if (params.scheme) {
        args.push("-scheme", params.scheme);
      }

      // Add destination
      if (params.destination) {
        args.push("-destination", params.destination);
      } else {
        // Auto-select appropriate destination
        const destination = await this.selectTestDestination();
        if (destination) {
          args.push("-destination", destination);
        }
      }

      // Add test plan
      if (params.testPlan) {
        args.push("-testPlan", params.testPlan);
      }

      // Add specific tests
      if (params.onlyTesting && params.onlyTesting.length > 0) {
        for (const test of params.onlyTesting) {
          args.push("-only-testing", test);
        }
      }

      // Skip specific tests
      if (params.skipTesting && params.skipTesting.length > 0) {
        for (const test of params.skipTesting) {
          args.push("-skip-testing", test);
        }
      }

      // Enable code coverage
      if (params.enableCodeCoverage) {
        args.push("-enableCodeCoverage", "YES");
      }

      // Parallel testing
      if (params.parallel) {
        args.push("-parallel-testing-enabled", "YES");
        if (params.maxConcurrency) {
          args.push(
            "-parallel-testing-worker-count",
            params.maxConcurrency.toString()
          );
        }
      }

      // Result bundle
      const resultBundlePath =
        params.resultBundlePath ||
        `${projectDir}/test-results-${Date.now()}.xcresult`;
      args.push("-resultBundlePath", resultBundlePath);

      // Execute tests
      const result = await this.executeCommand("xcrun", args, {
        cwd: projectDir,
        timeout: 1800000, // 30 minutes
      });

      const testTime = Date.now() - startTime;

      // Parse test results
      const testResults = await this.parseTestResults(
        resultBundlePath,
        result.stdout,
        result.stderr
      );

      // Generate detailed report if requested
      let detailedReport;
      if (params.generateReport) {
        detailedReport = await this.generateTestReport(resultBundlePath);
      }

      return this.createSuccessResponse(`Tests completed in ${testTime}ms`, {
        testTime,
        resultBundlePath,
        ...testResults,
        detailedReport,
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      return this.createErrorResponse(
        "Test execution failed",
        error instanceof Error ? error : new Error(String(error))
      );
    }
  }

  private async selectTestDestination(): Promise<string | null> {
    try {
      // Get available destinations
      const result = await this.executeCommand("xcrun", [
        "xcodebuild",
        "-showdestinations",
        "-scheme",
        "test",
      ]);

      // Parse and select first available iOS Simulator
      const lines = result.stdout.split("\n");
      for (const line of lines) {
        if (line.includes("iOS Simulator") && line.includes("iPhone")) {
          const match = line.match(/platform:([^,]+),.*name:([^,]+)/);
          if (match) {
            return `platform=${match[1].trim()},name=${match[2].trim()}`;
          }
        }
      }
    } catch (error) {
      console.warn("Could not auto-select test destination:", error);
    }

    return null;
  }

  private async parseTestResults(
    resultBundlePath: string,
    stdout: string,
    stderr: string
  ): Promise<{
    totalTests: number;
    passedTests: number;
    failedTests: number;
    skippedTests: number;
    testSuites: Array<{
      name: string;
      tests: number;
      failures: number;
      time: number;
    }>;
    failures: string[];
  }> {
    // Parse from stdout/stderr first
    const lines = (stdout + "\n" + stderr).split("\n");
    let totalTests = 0;
    let passedTests = 0;
    let failedTests = 0;
    let skippedTests = 0;
    const failures: string[] = [];
    const testSuites: Array<{
      name: string;
      tests: number;
      failures: number;
      time: number;
    }> = [];

    for (const line of lines) {
      if (line.includes("Test Suite") && line.includes("started")) {
        // Extract test suite info
      } else if (line.includes("executed") && line.includes("test")) {
        const match = line.match(/(\d+) tests?, with (\d+) failures?/);
        if (match) {
          totalTests += parseInt(match[1], 10);
          failedTests += parseInt(match[2], 10);
          passedTests = totalTests - failedTests;
        }
      } else if (line.includes("FAIL") || line.includes("failed")) {
        failures.push(line.trim());
      }
    }

    // Try to get more detailed info from xcresult bundle
    try {
      const xcresultInfo = await this.parseXCResultBundle(resultBundlePath);
      if (xcresultInfo) {
        return xcresultInfo;
      }
    } catch (error) {
      console.warn("Could not parse xcresult bundle:", error);
    }

    return {
      totalTests,
      passedTests,
      failedTests,
      skippedTests,
      testSuites,
      failures,
    };
  }

  private async parseXCResultBundle(bundlePath: string): Promise<any> {
    try {
      const result = await this.executeCommand("xcrun", [
        "xcresulttool",
        "get",
        "--format",
        "json",
        "--path",
        bundlePath,
      ]);

      return JSON.parse(result.stdout);
    } catch (error) {
      return null;
    }
  }

  private async generateTestReport(resultBundlePath: string): Promise<any> {
    try {
      // Generate HTML report using xcresulttool
      const reportPath = `${resultBundlePath}_report.html`;

      await this.executeCommand("xcrun", [
        "xcresulttool",
        "export",
        "--type",
        "html",
        "--path",
        resultBundlePath,
        "--output-path",
        reportPath,
      ]);

      return {
        htmlReportPath: reportPath,
        generated: true,
      };
    } catch (error) {
      return {
        error: "Could not generate HTML report",
        generated: false,
      };
    }
  }
}

/**
 * Register advanced build system tools (replaces basic build tools)
 */
export function registerAdvancedBuildTools(server: XcodeServer): void {
  const buildTool = new AdvancedBuildTool(server);
  const testTool = new AdvancedTestTool(server);

  buildTool.register();
  testTool.register();
}

/**
 * @deprecated Use registerAdvancedBuildTools instead
 */
export function registerEnterpriseBuildTools(server: XcodeServer): void {
  registerAdvancedBuildTools(server);
}
